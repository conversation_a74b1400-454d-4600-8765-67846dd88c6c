import 'dart:io';
import 'dart:typed_data';

import 'package:flower_timemachine/common/file_utils.dart';
import 'package:flower_timemachine/common/resources.dart';
import 'package:flower_timemachine/controller/nurture_types_controller.dart';
import 'package:flower_timemachine/models/flower.dart';
import 'package:flower_timemachine/repository/monthly_nurture_cycle_repo.dart';
import 'package:flower_timemachine/models/nurture_types.dart';
import 'package:flower_timemachine/models/tag_info.dart';
import 'package:flower_timemachine/types/flower_monthly_cycles.dart';
import 'package:flutter/material.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';
import 'package:collection/collection.dart';

part 'add_flower_controller.g.dart';

@riverpod
AddFlowerController getAddFlowerController(GetAddFlowerControllerRef ref, Flower? flower) {
  return AddFlowerController.create(flower);
}

@riverpod
FutureOr<FlowerMonthlyCycles> loadFlowerMonthlyCycles(
    LoadFlowerMonthlyCyclesRef ref, AddFlowerController controller) async {
  return await controller.loadFlowerMonthlyCycles();
}

class AddFlowerController {
  final Flower? flower;

  FlowerMonthlyCycles monthlyCycles = FlowerMonthlyCycles();
  Uint8List? _newAvatarData;
  String? _newName;
  bool _isChange = false;
  bool _isLoadMonthlyCycles = false;
  List<TagInfo>? _newSelectedTags;
  DateTime? _newArrivalTime;

  AddFlowerController._(this.flower);

  static AddFlowerController create(Flower? flower) {
    if (flower == null) {
      return AddFlowerController._(flower);
    } else {
      return AddFlowerController._(flower);
    }
  }

  Future<FlowerMonthlyCycles> loadFlowerMonthlyCycles() async {
    if (_isLoadMonthlyCycles) {
      return monthlyCycles;
    }

    // 新建花时使用默认配置
    if (flower == null) {
      monthlyCycles = FlowerMonthlyCycles();
      _isLoadMonthlyCycles = true;
      return monthlyCycles;
    }

    // 编辑花时使用已经修改过的配置
    monthlyCycles = await MonthlyNurtureCycleRepository.getFlowerMonthlyCycles(flower!.id);
    _isLoadMonthlyCycles = true;
    return monthlyCycles;
  }

  int getNurtureTypeCycle(NurtureType type) {
    final now = DateTime.now();
    return monthlyCycles.getEffectiveCycleForTypeAndMonth(type, now.month);
  }

  void setNurtureTypeCycle(NurtureType type, int cycle) {
    _isChange = true;
    monthlyCycles.setCycleForTypeAndMonth(type, 1, cycle);
  }

  void setNewAvatar(Uint8List image) {
    _isChange = true;
    _newAvatarData = image;
  }

  Future<Flower> save() async {
    String? avatarFile;
    if (_newAvatarData != null) {
      final littleData = await FlutterImageCompress.compressWithList(_newAvatarData!, minHeight: 256, minWidth: 256);
      avatarFile = await writeToAvatarRecordDir(littleData);
    }

    if (flower == null) {
      return await _createFlower(avatarFile);
    } else {
      await _updateFlower(avatarFile);
      return flower!;
    }
  }

  Future<Flower> _createFlower(String? avatarFile) async {
    int? arrivalTime;
    if (_newArrivalTime != null) {
      arrivalTime = (_newArrivalTime!.millisecondsSinceEpoch / 1000).truncate();
    }

    return Flower.create(
        name: _newName!,
        avatar: avatarFile,
        tags: _newSelectedTags,
        monthlyCycles: monthlyCycles,
        arrivalTime: arrivalTime);
  }

  Future<void> _updateFlower(String? avatarFile) async {
    int? arrivalTime;
    if (_newArrivalTime != null) {
      arrivalTime = (_newArrivalTime!.millisecondsSinceEpoch / 1000).truncate();
    }

    flower!.update(
        name: _newName,
        avatar: avatarFile,
        tags: _newSelectedTags,
        monthlyCycles: monthlyCycles,
        arrivalTime: arrivalTime);
  }

  bool isChange() => _isChange;

  Image get avatar {
    final avatar = flower?.avatar;
    if (avatar != null) {
      return Image.file(File(avatar));
    } else {
      return Image.asset(R.iconFlower);
    }
  }

  List<TagInfo> get selectedTags => List.unmodifiable(flower?.tags ?? []);

  set selectedTags(List<TagInfo> newTags) {
    _newSelectedTags = [];
    _newSelectedTags!.addAll(newTags);

    final currentTags = flower?.tags ?? [];
    if (const ListEquality().equals(currentTags, newTags)) {
      return;
    }
    _isChange = true;
  }

  String? get name {
    return _newName ?? flower?.name;
  }

  set name(String? v) {
    if (flower == null && (v == null || v == "")) {
      return;
    }
    if (v == flower?.name) {
      return;
    }

    _isChange = true;
    _newName = v;
  }

  bool get loadNurtureCycle => _isLoadMonthlyCycles;

  DateTime get arrivalTime {
    if (_newArrivalTime != null) {
      return _newArrivalTime!;
    }

    if (flower?.arrivalTime != null) {
      return DateTime.fromMillisecondsSinceEpoch(flower!.arrivalTime! * 1000);
    }
    if (flower?.createTime != null) {
      return DateTime.fromMillisecondsSinceEpoch(flower!.createTime * 1000);
    }

    return DateTime.now();
  }

  set arrivalTime(DateTime time) {
    if (_newArrivalTime == time) {
      return;
    }

    _newArrivalTime = time;
    _isChange = true;
  }
}
